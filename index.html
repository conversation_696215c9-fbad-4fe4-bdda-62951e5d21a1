<!DOCTYPE html>
<html lang="en" class="scroll-smooth">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes" />
  <title><PERSON><PERSON> | Software Developer</title>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" />
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      darkMode: 'class',
      theme: {
        extend: {
          fontFamily: {
            'poppins': ['Poppins', 'sans-serif'],
          },
          colors: {
            'dark-bg': '#0a0a0a',
            'dark-surface': '#111111',
            'dark-card': '#1a1a1a',
            'dark-border': '#2a2a2a',
            'accent-primary': '#6366f1',
            'accent-secondary': '#8b5cf6',
            'success': '#10b981',
            'warning': '#f59e0b',
            'surface-light': '#fafafa',
          },
          animation: {
            'fade-slide-in': 'fadeSlideIn 1s ease forwards',
            'float': 'float 6s ease-in-out infinite',
            'glow': 'glow 2s ease-in-out infinite alternate',
          },
          keyframes: {
            fadeSlideIn: {
              '0%': { opacity: '0', transform: 'translateY(30px)' },
              '100%': { opacity: '1', transform: 'translateY(0)' },
            },
            float: {
              '0%, 100%': { transform: 'translateY(0px)' },
              '50%': { transform: 'translateY(-10px)' },
            },
            glow: {
              '0%': { boxShadow: '0 0 20px rgba(99, 102, 241, 0.3)' },
              '100%': { boxShadow: '0 0 30px rgba(99, 102, 241, 0.6)' },
            }
          },
          boxShadow: {
            'glow': '0 0 20px rgba(99, 102, 241, 0.3)',
            'glow-lg': '0 0 40px rgba(99, 102, 241, 0.4)',
            'card': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
            'card-hover': '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
          }
        }
      }
    }
  </script>
  <style>
    /* CSS Custom Properties for Mobile Viewport */
    :root {
      --vh: 1vh;
      --mobile-nav-height: 60px;
    }

    /* Modern gradients and effects */
    .hero-gradient {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .dark .hero-gradient {
      background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    }
    
    .skill-gradient {
      background: linear-gradient(145deg, #ffffff, #f8fafc);
      border: 1px solid #e2e8f0;
    }
    .dark .skill-gradient {
      background: linear-gradient(145deg, #1e293b, #0f172a);
      border: 1px solid #334155;
    }
    
    .glass-effect {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .dark .glass-effect {
      background: rgba(0, 0, 0, 0.2);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .button-gradient {
      background: linear-gradient(135deg, #6366f1, #8b5cf6);
    }
    
    .button-gradient:hover {
      background: linear-gradient(135deg, #0307ef, #7c3aed);
      transform: translateY(-2px);
    }
    
    .text-gradient {
      background: linear-gradient(135deg, #ac3ef1, #4a37f8);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    .floating-card {
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    .floating-card:hover {
      transform: translateY(-8px) scale(1.02);
    }
    
    .project-overlay {
      background: linear-gradient(135deg, rgba(99, 102, 241, 0.9), rgba(139, 92, 246, 0.9));
      backdrop-filter: blur(10px);
    }
    
    /* Custom scrollbar */
    ::-webkit-scrollbar {
      width: 8px;
    }

    ::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    .dark ::-webkit-scrollbar-track {
      background: #1a1a1a;
    }

    ::-webkit-scrollbar-thumb {
      background: linear-gradient(135deg, #6366f1, #8b5cf6);
      border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb:hover {
      background: linear-gradient(135deg, #4f46e5, #7c3aed);
    }

    /* Enhanced form styling */
    .form-field-valid {
      border-color: #10b981 !important;
      box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
    }

    .form-field-invalid {
      border-color: #ef4444 !important;
      box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
    }

    .form-field-focus {
      border-color: #6366f1 !important;
      box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1) !important;
    }

    /* Loading button animation */
    .btn-loading {
      position: relative;
      pointer-events: none;
    }

    .btn-loading::after {
      content: '';
      position: absolute;
      width: 16px;
      height: 16px;
      margin: auto;
      border: 2px solid transparent;
      border-top-color: #ffffff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Success/Error message animations */
    .message-slide-in {
      animation: slideInUp 0.5s ease-out;
    }

    @keyframes slideInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* Form field hover effects */
    .contact-form input:hover,
    .contact-form textarea:hover,
    .contact-form select:hover {
      border-color: #8b5cf6;
      transition: border-color 0.3s ease;
    }

    /* Enhanced Mobile Compatibility - All Devices */
    @media (max-width: 480px) {
      /* Extra small phones */
      .hero-gradient h1 {
        font-size: 1.75rem !important;
        line-height: 1.1 !important;
        margin-bottom: 0.5rem !important;
      }

      .hero-gradient p {
        font-size: 0.9rem !important;
        line-height: 1.3 !important;
      }

      .w-48.h-48 {
        width: 6rem !important;
        height: 6rem !important;
      }

      .text-2xl {
        font-size: 1rem !important;
      }

      .text-3xl {
        font-size: 1.25rem !important;
      }

      .text-4xl {
        font-size: 1.5rem !important;
      }

      .px-6 {
        padding-left: 1rem !important;
        padding-right: 1rem !important;
      }

      .py-20 {
        padding-top: 1.5rem !important;
        padding-bottom: 1.5rem !important;
      }
    }

    /* Aggressive Mobile text spacing optimization */
    @media (max-width: 768px) {
      /* Hero section - much tighter */
      .hero-gradient h1 {
        line-height: 1.1 !important;
        margin-bottom: 0.5rem !important;
        font-size: 2rem !important;
      }

      .hero-gradient p {
        line-height: 1.3 !important;
        margin-bottom: 1rem !important;
        font-size: 1rem !important;
      }

      .hero-gradient .glass-effect {
        margin-bottom: 1rem !important;
        padding: 0.75rem 1rem !important;
      }

      /* Text sizes - much smaller */
      .text-lg {
        font-size: 0.9rem !important;
        line-height: 1.4 !important;
      }

      .text-xl {
        font-size: 1rem !important;
        line-height: 1.3 !important;
      }

      .text-2xl {
        font-size: 1.125rem !important;
        line-height: 1.2 !important;
      }

      .text-3xl {
        font-size: 1.5rem !important;
        line-height: 1.2 !important;
      }

      .text-4xl {
        font-size: 1.75rem !important;
        line-height: 1.1 !important;
      }

      /* Margins - much tighter */
      .mb-4 {
        margin-bottom: 0.5rem !important;
      }

      .mb-6 {
        margin-bottom: 0.75rem !important;
      }

      .mb-8 {
        margin-bottom: 1rem !important;
      }

      .mb-12 {
        margin-bottom: 1.5rem !important;
      }

      .mb-16 {
        margin-bottom: 2rem !important;
      }

      /* Section padding - much smaller */
      .py-20 {
        padding-top: 2rem !important;
        padding-bottom: 2rem !important;
      }

      /* Space between elements */
      .space-y-6 > * + * {
        margin-top: 0.75rem !important;
      }

      .space-y-8 > * + * {
        margin-top: 1rem !important;
      }

      /* Glass effects - smaller padding */
      .glass-effect {
        padding: 1rem !important;
      }

      /* Cards - tighter spacing */
      .floating-card {
        margin-bottom: 0.75rem !important;
        padding: 1rem !important;
      }

      /* Project descriptions */
      .project-overlay p {
        line-height: 1.3 !important;
        margin-bottom: 0.5rem !important;
        font-size: 0.875rem !important;
      }

      /* About section specific */
      .text-justify {
        line-height: 1.4 !important;
        margin-bottom: 0.75rem !important;
      }

      /* Skills grid - tighter */
      .skill-gradient {
        padding: 1rem !important;
      }

      .skill-gradient h3 {
        font-size: 0.875rem !important;
        margin-top: 0.5rem !important;
      }

      /* Contact form - tighter */
      .contact-form .space-y-6 > * + * {
        margin-top: 0.75rem !important;
      }

      .contact-form input,
      .contact-form textarea,
      .contact-form select {
        padding: 0.75rem !important;
        font-size: 0.9rem !important;
      }

      /* Navigation - smaller */
      nav .py-4 {
        padding-top: 0.75rem !important;
        padding-bottom: 0.75rem !important;
      }

      /* Footer - tighter */
      footer .py-12 {
        padding-top: 2rem !important;
        padding-bottom: 2rem !important;
      }

      /* Remove extra spacing from containers */
      .max-w-7xl,
      .max-w-4xl {
        padding-left: 1rem !important;
        padding-right: 1rem !important;
      }

      /* Hero section specific mobile optimizations */
      .min-h-screen {
        min-height: 80vh !important;
      }

      /* Fix profile image positioning on mobile */
      #header {
        padding-top: 6rem !important; /* Increased padding for mobile */
        min-height: calc(var(--vh, 1vh) * 100) !important; /* Use custom viewport height */
      }

      /* Ensure profile image is not hidden behind nav */
      #header .relative.z-10 {
        padding-top: 2rem !important;
      }

      /* Button spacing */
      .flex.gap-4 {
        gap: 0.5rem !important;
      }

      /* Profile image smaller on mobile */
      .w-48.h-48 {
        width: 8rem !important;
        height: 8rem !important;
      }

      /* Reduce icon margins */
      .mr-3 {
        margin-right: 0.5rem !important;
      }

      /* Tighter grid spacing */
      .grid.gap-6 {
        gap: 0.75rem !important;
      }

      .grid.gap-8 {
        gap: 1rem !important;
      }

      /* Education and project cards */
      .rounded-3xl {
        border-radius: 1rem !important;
      }

      /* Contact info cards */
      .text-3xl {
        font-size: 1.5rem !important;
      }

      /* Social links smaller */
      .w-12.h-12 {
        width: 2.5rem !important;
        height: 2.5rem !important;
      }

      /* Enhanced touch targets for mobile */
      .mobile-nav-link {
        min-height: 48px !important;
        touch-action: manipulation !important;
      }

      /* Better button spacing on mobile */
      .flex.gap-2 {
        gap: 0.25rem !important;
      }

      .flex.gap-4 {
        gap: 0.5rem !important;
      }

      /* Improved form fields for mobile */
      .contact-form input,
      .contact-form textarea,
      .contact-form select {
        min-height: 48px !important;
        font-size: 16px !important; /* Prevents zoom on iOS */
        -webkit-appearance: none !important;
        appearance: none !important;
        border-radius: 8px !important;
      }

      /* Better project card layout */
      .grid.md\\:grid-cols-2.lg\\:grid-cols-3 {
        grid-template-columns: 1fr !important;
      }

      .grid.md\\:grid-cols-3 {
        grid-template-columns: repeat(2, 1fr) !important;
      }

      .grid.sm\\:grid-cols-2 {
        grid-template-columns: 1fr !important;
      }

      /* Ensure images are responsive */
      img {
        max-width: 100% !important;
        height: auto !important;
      }

      /* Fix overflow issues */
      body {
        overflow-x: hidden !important;
      }

      /* Better scroll behavior */
      html {
        scroll-behavior: smooth !important;
        -webkit-overflow-scrolling: touch !important;
      }

      /* Improve tap targets */
      a, button {
        min-height: 44px !important;
        min-width: 44px !important;
        touch-action: manipulation !important;
      }

      /* Fix sticky navigation on mobile */
      nav {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        z-index: 50 !important;
      }

      /* Ensure content doesn't hide behind nav */
      main, section {
        scroll-margin-top: 80px !important;
      }
    }

    /* Mobile menu styles */
    .hamburger {
      display: none;
    }

    @media (max-width: 768px) {
      .hamburger {
        display: flex;
        flex-direction: column;
        cursor: pointer;
        z-index: 60;
        position: relative;
        padding: 8px;
        border-radius: 8px;
        transition: all 0.3s ease;
      }

      .hamburger:hover {
        background: rgba(99, 102, 241, 0.1);
      }

      .hamburger span {
        width: 25px;
        height: 3px;
        background: currentColor;
        margin: 3px 0;
        transition: 0.3s;
        border-radius: 2px;
      }

      .hamburger.active span:nth-child(1) {
        transform: rotate(-45deg) translate(-5px, 6px);
      }

      .hamburger.active span:nth-child(2) {
        opacity: 0;
      }

      .hamburger.active span:nth-child(3) {
        transform: rotate(45deg) translate(-5px, -6px);
      }

      /* Mobile navigation menu - Fixed for all 6 items */
      .nav-links {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        background: rgba(255, 255, 255, 0.98) !important;
        backdrop-filter: blur(20px) !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
        transform: translateX(100%) !important;
        transition: transform 0.3s ease-in-out !important;
        z-index: 55 !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        overflow-y: auto !important;
        padding: 20px 0 !important;
      }

      .dark .nav-links {
        background: rgba(17, 24, 39, 0.98) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
      }

      .nav-links.active {
        transform: translateX(0) !important;
      }

      /* Ensure all navigation items are visible */
      .nav-links li {
        display: block !important;
        width: 100% !important;
        max-width: 250px !important;
        margin: 6px 0 !important;
      }

      /* Hide desktop navigation on mobile */
      .hidden.md\\:flex {
        display: none !important;
      }

      /* Mobile navigation links styling - Enhanced visibility */
      .mobile-nav-link {
        display: flex !important;
        align-items: center !important;
        justify-content: flex-start !important;
        width: 100% !important;
        padding: 16px 24px !important;
        margin: 0 !important;
        border-radius: 16px !important;
        font-size: 1.25rem !important;
        font-weight: 700 !important;
        color: #111827 !important;
        background: rgba(255, 255, 255, 0.15) !important;
        border: 2px solid rgba(99, 102, 241, 0.3) !important;
        backdrop-filter: blur(10px) !important;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4) !important;
        transition: all 0.3s ease !important;
        text-decoration: none !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
      }

      .dark .mobile-nav-link {
        color: #ffffff !important;
        background: rgba(255, 255, 255, 0.1) !important;
        border-color: rgba(139, 92, 246, 0.4) !important;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
      }

      .mobile-nav-link:hover {
        background: rgba(99, 102, 241, 0.25) !important;
        border-color: rgba(99, 102, 241, 0.7) !important;
        color: #6366f1 !important;
        transform: scale(1.03) !important;
        box-shadow: 0 4px 16px rgba(99, 102, 241, 0.2) !important;
      }

      .mobile-nav-link.active {
        background: rgba(99, 102, 241, 0.3) !important;
        border-color: rgba(99, 102, 241, 0.9) !important;
        color: #6366f1 !important;
        transform: scale(1.05) !important;
        box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4) !important;
      }

      .mobile-nav-link i {
        margin-right: 16px !important;
        font-size: 1.25rem !important;
        color: #6366f1 !important;
        min-width: 24px !important;
        text-align: center !important;
      }

    }

    /* Navigation border line */
    .nav-border::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 30%;
      height: 2px;
      background: linear-gradient(90deg, #6366f1, #8b5cf6);
      opacity: 0.8;
      border-radius: 1px;
    }

    /* Additional Mobile Optimizations for Various Screen Sizes */
    @media (max-width: 375px) {
      /* iPhone SE and smaller devices */
      .hero-gradient h1 {
        font-size: 1.5rem !important;
      }

      .px-6 {
        padding-left: 0.75rem !important;
        padding-right: 0.75rem !important;
      }

      .mobile-nav-link {
        font-size: 1rem !important;
        padding: 12px 16px !important;
      }

      .glass-effect {
        padding: 0.75rem !important;
      }
    }

    @media (max-width: 320px) {
      /* Very small devices */
      .hero-gradient h1 {
        font-size: 1.25rem !important;
      }

      .text-gradient {
        font-size: 1rem !important;
      }

      .px-6 {
        padding-left: 0.5rem !important;
        padding-right: 0.5rem !important;
      }
    }

    /* Landscape orientation optimizations */
    @media (max-height: 500px) and (orientation: landscape) {
      .min-h-screen {
        min-height: 100vh !important;
      }

      .py-20 {
        padding-top: 1rem !important;
        padding-bottom: 1rem !important;
      }

      .hero-gradient {
        padding-top: 1rem !important;
      }

      .w-48.h-48 {
        width: 4rem !important;
        height: 4rem !important;
      }
    }

    /* High DPI displays */
    @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
      .glass-effect {
        backdrop-filter: blur(20px) !important;
      }
    }

    /* Dark mode mobile optimizations */
    @media (max-width: 768px) and (prefers-color-scheme: dark) {
      .mobile-nav-link {
        background: rgba(255, 255, 255, 0.05) !important;
        border-color: rgba(139, 92, 246, 0.3) !important;
      }
    }
  </style>
</head>

<body class="bg-surface-light dark:bg-dark-bg text-gray-900 dark:text-white font-poppins transition-colors duration-300">
  
  <!-- Modern Navigation with Glass Effect -->
  <nav class="fixed top-0 left-0 right-0 z-50 glass-effect backdrop-blur-md nav-border">
    <div class="max-w-7xl mx-auto px-6 py-4">
      <div class="flex justify-between items-center">
        <!-- Logo -->
        <div class="text-2xl font-bold text-gradient">
          Arijit Dey
        </div>

        <!-- Desktop Navigation -->
        <ul class="hidden md:flex space-x-8">
          <li><a href="#header" class="nav-link text-gray-700 dark:text-gray-300 hover:text-accent-primary transition-all duration-300 font-medium relative group">
            Home
            <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-accent-primary to-accent-secondary transition-all duration-300 group-hover:w-full"></span>
          </a></li>
          <li><a href="#about" class="nav-link text-gray-700 dark:text-gray-300 hover:text-accent-primary transition-all duration-300 font-medium relative group">
            About
            <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-accent-primary to-accent-secondary transition-all duration-300 group-hover:w-full"></span>
          </a></li>
          <li><a href="#skills" class="nav-link text-gray-700 dark:text-gray-300 hover:text-accent-primary transition-all duration-300 font-medium relative group">
            Skills
            <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-accent-primary to-accent-secondary transition-all duration-300 group-hover:w-full"></span>
          </a></li>
          <li><a href="#education" class="nav-link text-gray-700 dark:text-gray-300 hover:text-accent-primary transition-all duration-300 font-medium relative group">
            Education
            <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-accent-primary to-accent-secondary transition-all duration-300 group-hover:w-full"></span>
          </a></li>
          <li><a href="#projects" class="nav-link text-gray-700 dark:text-gray-300 hover:text-accent-primary transition-all duration-300 font-medium relative group">
            Projects
            <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-accent-primary to-accent-secondary transition-all duration-300 group-hover:w-full"></span>
          </a></li>
          <li><a href="#contact" class="nav-link text-gray-700 dark:text-gray-300 hover:text-accent-primary transition-all duration-300 font-medium relative group">
            Contact
            <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-accent-primary to-accent-secondary transition-all duration-300 group-hover:w-full"></span>
          </a></li>
        </ul>

        <!-- Mobile Hamburger -->
        <button class="hamburger md:hidden" id="hamburger">
          <span></span>
          <span></span>
          <span></span>
        </button>
      </div>

      <!-- Mobile Navigation -->
      <!-- Mobile Navigation Menu -->
      <ul class="nav-links fixed top-0 left-0 w-full h-full bg-white/98 dark:bg-gray-900/98 backdrop-blur-xl
                 flex flex-col justify-center items-center space-y-6 transform translate-x-full
                 transition-transform duration-300 ease-in-out z-50" id="nav-links">
        <li><a href="#header" class="mobile-nav-link text-xl font-semibold text-gray-900 dark:text-white hover:text-accent-primary
                                   transition-all duration-300 hover:scale-105 relative px-6 py-3 rounded-xl
                                   flex items-center justify-start min-w-[180px]">
          <i class="fas fa-home mr-3 text-accent-primary text-lg"></i>Home</a></li>
        <li><a href="#about" class="mobile-nav-link text-xl font-semibold text-gray-900 dark:text-white hover:text-accent-primary
                                  transition-all duration-300 hover:scale-105 relative px-6 py-3 rounded-xl
                                  flex items-center justify-start min-w-[180px]">
          <i class="fas fa-user mr-3 text-accent-primary text-lg"></i>About</a></li>
        <li><a href="#skills" class="mobile-nav-link text-xl font-semibold text-gray-900 dark:text-white hover:text-accent-primary
                                   transition-all duration-300 hover:scale-105 relative px-6 py-3 rounded-xl
                                   flex items-center justify-start min-w-[180px]">
          <i class="fas fa-cogs mr-3 text-accent-primary text-lg"></i>Skills</a></li>
        <li><a href="#education" class="mobile-nav-link text-xl font-semibold text-gray-900 dark:text-white hover:text-accent-primary
                                      transition-all duration-300 hover:scale-105 relative px-6 py-3 rounded-xl
                                      flex items-center justify-start min-w-[180px]">
          <i class="fas fa-graduation-cap mr-3 text-accent-primary text-lg"></i>Education</a></li>
        <li><a href="#projects" class="mobile-nav-link text-xl font-semibold text-gray-900 dark:text-white hover:text-accent-primary
                                     transition-all duration-300 hover:scale-105 relative px-6 py-3 rounded-xl
                                     flex items-center justify-start min-w-[180px]">
          <i class="fas fa-laptop-code mr-3 text-accent-primary text-lg"></i>Projects</a></li>
        <li><a href="#contact" class="mobile-nav-link text-xl font-semibold text-gray-900 dark:text-white hover:text-accent-primary
                                    transition-all duration-300 hover:scale-105 relative px-6 py-3 rounded-xl
                                    flex items-center justify-start min-w-[180px]">
          <i class="fas fa-envelope mr-3 text-accent-primary text-lg"></i>Contact</a></li>
      </ul>
    </div>
  </nav>

  

  <!-- Modern Hero Section -->
  <header class="min-h-screen hero-gradient relative overflow-hidden pt-24 sm:pt-20" id="header">
    <!-- Animated Background Elements -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-accent-primary opacity-20 rounded-full blur-3xl animate-float"></div>
      <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-accent-secondary opacity-20 rounded-full blur-3xl animate-float" style="animation-delay: 2s;"></div>
    </div>

    <!-- Theme Toggle Button -->
    <button id="toggle-theme" aria-label="Toggle Light/Dark Mode"
            class="fixed bottom-6 right-6 z-50 glass-effect w-14 h-14 rounded-full font-medium cursor-pointer
                   shadow-glow hover:shadow-glow-lg transition-all duration-300 text-xl
                   text-gray-700 dark:text-gray-300 hover:text-accent-primary
                   flex items-center justify-center hover:scale-110">
      <i class="fas fa-sun dark:hidden transition-transform duration-300"></i>
      <i class="fas fa-moon hidden dark:inline transition-transform duration-300"></i>
    </button>

    <div class="relative z-10 flex flex-col items-center justify-center min-h-screen px-6 text-center">
      <!-- Profile Image with Modern Effects -->
      <div class="mb-8 relative">
        <div class="absolute inset-0 bg-gradient-to-r from-accent-primary to-accent-secondary rounded-full blur-lg opacity-30 animate-glow"></div>
        <img src="public/front.png" alt="Arijit Dey"
             class="relative w-48 h-48 md:w-64 md:h-64 rounded-full object-cover border-4 border-white/20
                    shadow-2xl transition-all duration-500 hover:scale-105 animate-float" />
      </div>

      <!-- Hero Text -->
      <div class="max-w-4xl mx-auto animate-fade-slide-in">
        <h1 class="text-2xl md:text-6xl lg:text-7xl font-bold mb-2 md:mb-6 text-white leading-tight">
          Hey, I'm <span class="text-gradient bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">Arijit Dey</span> 👋
        </h1>

        <p class="text-base md:text-2xl lg:text-3xl font-medium mb-4 md:mb-8 text-white/90">
          <i class="fas fa-code mr-2 text-accent-primary"></i>
          <span class="text-gradient bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
            Software Developer & Cloud Enthusiast
          </span>
        </p>

        <div class="glass-effect px-4 py-2 md:px-6 md:py-4 rounded-2xl inline-block mb-4 md:mb-8">
          <code class="text-sm md:text-lg font-mono text-white/90">
            Turning ideas into code, one line at a time.
          </code>
        </div>

        <!-- CTA Buttons -->
        <div class="flex flex-col sm:flex-row gap-2 md:gap-4 justify-center items-center">
          <a href="#about" class="glass-effect px-8 py-4 rounded-full text-white font-semibold
                                 shadow-glow hover:shadow-glow-lg transition-all duration-300
                                 hover:-translate-y-1 inline-flex items-center gap-2">
            <i class="fas fa-user"></i>
            About Me
          </a>
          <a href="#projects" class="glass-effect px-8 py-4 rounded-full text-white font-semibold
                                   shadow-glow hover:shadow-glow-lg border border-white/20 hover:border-white/40 transition-all duration-300
                                   hover:-translate-y-1 inline-flex items-center gap-2">
            <i class="fas fa-laptop-code"></i>
            View Projects
          </a>
       <a href="https://drive.google.com/file/d/10Dee_9l-lYkZ80SEbP2y4EZj_tkRWnNU/view?usp=sharing" target="_blank" rel="noopener noreferrer"
   class="btn btn2 glass-effect px-8 py-4 rounded-full text-white font-semibold
          shadow-glow hover:shadow-glow-lg border border-white/20 hover:border-white/40 transition-all duration-300
          hover:-translate-y-1 inline-flex items-center gap-2">
   <i class="fas fa-download"></i>
   Download Resume
</a>


        </div>
      </div>

      <!-- Scroll Indicator -->
     
    </div>
  </header>

  <!-- Modern About Section -->
  <section id="about" class="py-20 bg-gray-50 dark:bg-dark-surface">
    <div class="max-w-7xl mx-auto px-6">
      <!-- Section Header -->
      <div class="text-center mb-12 md:mb-16">
        <h2 class="text-3xl md:text-5xl font-bold mb-4">
          <span class="text-gradient">About Me</span>
        </h2>
        <div class="w-24 h-1 bg-gradient-to-r from-accent-primary to-accent-secondary mx-auto rounded-full"></div>
      </div>

      <div class="grid lg:grid-cols-2 gap-12 items-center">
        <!-- Image Section -->
        <div class="relative">
          <div class="absolute inset-0 bg-gradient-to-r from-accent-primary to-accent-secondary rounded-3xl blur-lg opacity-20"></div>
          <div class="relative glass-effect rounded-3xl p-8 floating-card">
            <img src="public/second.jpg" alt="Arijit Dey"
                 class="w-full h-auto rounded-2xl shadow-card hover:shadow-card-hover
                        transition-all duration-500" />
          </div>
        </div>

       <!-- Content Section -->
        <div class="space-y-6">
          <div class="glass-effect rounded-2xl p-8 floating-card">
            <h3 class="text-2xl font-bold mb-4 text-gray-800 dark:text-white">
              <i class="fas fa-user text-accent-primary mr-3"></i>
              Who I Am
            </h3>
            <!-- Enhanced mobile-optimized paragraphs -->
            <div class="space-y-4 md:space-y-6">
              <p class="text-sm md:text-lg leading-6 md:leading-8 text-gray-600 dark:text-gray-300 text-left md:text-justify">
                I'm a motivated <span class="text-accent-primary font-semibold">Cloud Computing enthusiast</span> and completed my degree in Computer Science engineering with specialization in cloud technologies. I have a strong foundation in programming languages like <span class="text-accent-secondary font-semibold">Java and Python</span>, complemented knowledge in building scalable, cloud-native solutions using <span class="text-accent-primary font-semibold">Microsoft Azure & AWS</span>.
              </p>

              <p class="text-sm md:text-lg leading-6 md:leading-8 text-gray-600 dark:text-gray-300 text-left md:text-justify">
                I'm also passionate about <span class="text-accent-secondary font-semibold">Artificial Intelligence and Machine Learning</span>, applying these technologies to real-world problems like image stitching, sentiment analysis, and intelligent data processing.
              </p>
            </div>

            <!-- Resume Download Button -->
            <div class="text-center">
             <a href="https://drive.google.com/file/d/10Dee_9l-lYkZ80SEbP2y4EZj_tkRWnNU/view?usp=sharing" 
   target="_blank" rel="noopener noreferrer"
   class="button-gradient px-6 py-3 rounded-full text-white font-semibold
          shadow-glow hover:shadow-glow-lg transition-all duration-300
          hover:-translate-y-1 inline-flex items-center gap-2">
  <i class="fas fa-download"></i>
  Download My Resume
</a>

            </div>
          </div>

          <!-- Info Cards -->
          <div class="grid sm:grid-cols-2 gap-4">
            <div class="glass-effect rounded-xl p-6 floating-card">
              <div class="flex items-center mb-3">
                <i class="fas fa-envelope text-accent-primary text-xl mr-3"></i>
                <span class="font-semibold text-gray-800 dark:text-white">Email</span>
              </div>
              <p class="text-gray-600 dark:text-gray-300"><EMAIL></p>
            </div>

            <div class="glass-effect rounded-xl p-6 floating-card">
              <div class="flex items-center mb-3">
                <i class="fas fa-map-marker-alt text-accent-primary text-xl mr-3"></i>
                <span class="font-semibold text-gray-800 dark:text-white">Location</span>
              </div>
              <p class="text-gray-600 dark:text-gray-300">Kolkata, India</p>
            </div>

            <div class="glass-effect rounded-xl p-6 floating-card">
              <div class="flex items-center mb-3">
                <i class="fas fa-briefcase text-accent-primary text-xl mr-3"></i>
                <span class="font-semibold text-gray-800 dark:text-white">Status</span>
              </div>
              <p class="text-success font-semibold">Open to opportunities</p>
            </div>

            <div class="glass-effect rounded-xl p-6 floating-card">
              <div class="flex items-center mb-3">
                <i class="fas fa-graduation-cap text-accent-primary text-xl mr-3"></i>
                <span class="font-semibold text-gray-800 dark:text-white">Degree</span>
              </div>
              <p class="text-gray-600 dark:text-gray-300">B.E Computer Science</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Modern Skills Section -->
  <section id="skills" class="py-20 bg-white dark:bg-dark-bg">
    <div class="max-w-7xl mx-auto px-6">
      <!-- Section Header -->
      <div class="text-center mb-12 md:mb-16">
        <h2 class="text-3xl md:text-5xl font-bold mb-4">
          <span class="text-gradient">Skills & Technologies</span>
        </h2>
        <div class="w-24 h-1 bg-gradient-to-r from-accent-primary to-accent-secondary mx-auto rounded-full"></div>
        <p class="text-gray-600 dark:text-gray-300 mt-4 text-base md:text-lg">Technologies I work with</p>
      </div>

      <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        <div class="skill-gradient p-6 rounded-2xl text-center floating-card group">
          <i class="fab fa-java text-4xl text-accent-primary mb-4 group-hover:scale-110 transition-transform duration-300"></i>
          <h3 class="font-semibold text-gray-800 dark:text-white">Core Java</h3>
        </div>

        <div class="skill-gradient p-6 rounded-2xl text-center floating-card group">
          <i class="fas fa-chart-bar text-4xl text-accent-primary mb-4 group-hover:scale-110 transition-transform duration-300"></i>
          <h3 class="font-semibold text-gray-800 dark:text-white">Power BI</h3>
        </div>

        <div class="skill-gradient p-6 rounded-2xl text-center floating-card group">
          <i class="fas fa-database text-4xl text-accent-primary mb-4 group-hover:scale-110 transition-transform duration-300"></i>
          <h3 class="font-semibold text-gray-800 dark:text-white">SQL</h3>
        </div>

        <div class="skill-gradient p-6 rounded-2xl text-center floating-card group">
          <i class="fas fa-file-excel text-4xl text-accent-primary mb-4 group-hover:scale-110 transition-transform duration-300"></i>
          <h3 class="font-semibold text-gray-800 dark:text-white">MS Excel</h3>
        </div>

        <div class="skill-gradient p-6 rounded-2xl text-center floating-card group">
          <i class="fab fa-microsoft text-4xl text-accent-primary mb-4 group-hover:scale-110 transition-transform duration-300"></i>
          <h3 class="font-semibold text-gray-800 dark:text-white">Microsoft Azure</h3>
        </div>

        <div class="skill-gradient p-6 rounded-2xl text-center floating-card group">
          <i class="fab fa-python text-4xl text-accent-primary mb-4 group-hover:scale-110 transition-transform duration-300"></i>
          <h3 class="font-semibold text-gray-800 dark:text-white">Python</h3>
        </div>

        <div class="skill-gradient p-6 rounded-2xl text-center floating-card group">
          <i class="fas fa-cogs text-4xl text-accent-primary mb-4 group-hover:scale-110 transition-transform duration-300"></i>
          <h3 class="font-semibold text-gray-800 dark:text-white">DSA</h3>
        </div>

        <div class="skill-gradient p-6 rounded-2xl text-center floating-card group">
          <i class="fab fa-html5 text-4xl text-accent-primary mb-4 group-hover:scale-110 transition-transform duration-300"></i>
          <h3 class="font-semibold text-gray-800 dark:text-white">HTML</h3>
        </div>

        <div class="skill-gradient p-6 rounded-2xl text-center floating-card group">
          <i class="fab fa-css3-alt text-4xl text-accent-primary mb-4 group-hover:scale-110 transition-transform duration-300"></i>
          <h3 class="font-semibold text-gray-800 dark:text-white">CSS</h3>
        </div>

        <div class="skill-gradient p-6 rounded-2xl text-center floating-card group">
          <i class="fas fa-paintbrush text-4xl text-accent-primary mb-4 group-hover:scale-110 transition-transform duration-300"></i>
          <h3 class="font-semibold text-gray-800 dark:text-white">Canva</h3>
        </div>

        <div class="skill-gradient p-6 rounded-2xl text-center floating-card group">
          <i class="fas fa-note-sticky text-4xl text-accent-primary mb-4 group-hover:scale-110 transition-transform duration-300"></i>
          <h3 class="font-semibold text-gray-800 dark:text-white">Agile Development</h3>
        </div>

        <div class="skill-gradient p-6 rounded-2xl text-center floating-card group">
          <i class="fab fa-figma text-4xl text-accent-primary mb-4 group-hover:scale-110 transition-transform duration-300"></i>
          <h3 class="font-semibold text-gray-800 dark:text-white">Figma</h3>
        </div>
      </div>
    </div>
  </section>

  <!-- Modern Education Section -->
  <section id="education" class="py-20 bg-gray-50 dark:bg-dark-surface">
    <div class="max-w-7xl mx-auto px-6">
      <!-- Section Header -->
      <div class="text-center mb-16">
        <h2 class="text-4xl md:text-5xl font-bold mb-4">
          <span class="text-gradient">Education</span>
        </h2>
        <div class="w-24 h-1 bg-gradient-to-r from-accent-primary to-accent-secondary mx-auto rounded-full"></div>
      </div>

      <div class="space-y-8">
        <!-- University Education -->
        <div class="glass-effect rounded-3xl p-8 floating-card">
          <div class="grid md:grid-cols-3 gap-8 items-center">
            <div class="relative">
              <div class="absolute inset-0 bg-gradient-to-r from-accent-primary to-accent-secondary rounded-2xl blur-lg opacity-20"></div>
              <img src="public/Cu.jpg" alt="Chandigarh University"
                   class="relative w-full h-48 object-cover rounded-2xl shadow-card" />
            </div>
            <div class="md:col-span-2">
              <h3 class="text-2xl font-bold text-gray-800 dark:text-white mb-4">
                <i class="fas fa-graduation-cap text-accent-primary mr-3"></i>
                Bachelor's of Engineering in Computer Science
              </h3>
              <div class="space-y-2 text-gray-600 dark:text-gray-300">
                <p><span class="font-semibold text-accent-primary">Institution:</span> Chandigarh University</p>
                <p><span class="font-semibold text-accent-primary">Specialization:</span> Cloud Computing</p>
                <p><span class="font-semibold text-accent-primary">Year:</span> 2021 - 2025</p>
                <p><span class="font-semibold text-accent-primary">Status:</span> <span class="text-success">Completed</span></p>
              </div>
            </div>
          </div>
        </div>

        <!-- School Education -->
        <div class="glass-effect rounded-3xl p-8 floating-card">
          <div class="grid md:grid-cols-3 gap-8 items-center">
            <div class="relative">
              <div class="absolute inset-0 bg-gradient-to-r from-accent-primary to-accent-secondary rounded-2xl blur-lg opacity-20"></div>
              <img src="public/school.jpg" alt="K.V No.2 Bhubaneswar"
                   class="relative w-full h-48 object-cover rounded-2xl shadow-card" />
            </div>
            <div class="md:col-span-2">
              <h3 class="text-2xl font-bold text-gray-800 dark:text-white mb-4">
                <i class="fas fa-school text-accent-primary mr-3"></i>
                Higher Secondary Education
              </h3>
              <div class="space-y-2 text-gray-600 dark:text-gray-300">
                <p><span class="font-semibold text-accent-primary">School:</span> K.V No.2 Bhubaneswar</p>
                <p><span class="font-semibold text-accent-primary">Stream:</span> PCM (Physics, Chemistry, Mathematics)</p>
                <p><span class="font-semibold text-accent-primary">Year:</span> 2020 - 2021</p>
                <p><span class="font-semibold text-accent-primary">Status:</span> <span class="text-success">Completed</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Modern Projects Section -->
  <section id="projects" class="py-20 bg-white dark:bg-dark-bg">
    <div class="max-w-7xl mx-auto px-6">
      <!-- Section Header -->
      <div class="text-center mb-16">
        <h2 class="text-4xl md:text-5xl font-bold mb-4">
          <span class="text-gradient">Featured Projects</span>
        </h2>
        <div class="w-24 h-1 bg-gradient-to-r from-accent-primary to-accent-secondary mx-auto rounded-full"></div>
        <p class="text-gray-600 dark:text-gray-300 mt-4 text-lg">Some of my recent work</p>
      </div>

      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Project 1 -->
        <div class="group relative overflow-hidden rounded-3xl bg-white dark:bg-dark-card shadow-card hover:shadow-card-hover transition-all duration-500 floating-card">
          <div class="relative h-64 overflow-hidden">
            <img src="public/Seam_Carving.jpg" alt="Image Stitching"
                 class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110" />
            <div class="project-overlay absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 flex flex-col justify-center items-center p-6 text-center">
              <h3 class="text-xl font-bold text-white mb-3">Image Stitching using Seam Carving</h3>
              <p class="text-white/90 text-sm mb-4 text-justify">Combining seam carving with image stitching techniques to build accurate panoramic views with minimal distortion.</p>
              <a href="https://github.com/Arijitag7/Image-Stitching-using-Seam-Carving-.git" target="_blank"
                 class="button-gradient px-6 py-2 rounded-full text-white font-semibold hover:-translate-y-1 transition-all duration-300 inline-flex items-center gap-2">
                <i class="fab fa-github"></i>
                View Code
              </a>
            </div>
          </div>
          <div class="p-6">
            <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-2">Image Stitching using Seam Carving</h3>
            <p class="text-gray-600 dark:text-gray-300 text-sm">Advanced computer vision project combining multiple techniques</p>
          </div>
        </div>

        <!-- Project 2 -->
        <div class="group relative overflow-hidden rounded-3xl bg-white dark:bg-dark-card shadow-card hover:shadow-card-hover transition-all duration-500 floating-card">
          <div class="relative h-64 overflow-hidden">
            <img src="public/creativo.jpg" alt="Criativo"
                 class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110" />
            <div class="project-overlay absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 flex flex-col justify-center items-center p-6 text-center">
              <h3 class="text-xl font-bold text-white mb-3">Criativo - Project Ideas Website</h3>
              <p class="text-white/90 text-sm mb-4 text-justify">A web platform for students to discover and contribute academic project ideas. Built with modern web technologies.</p>
              <a href="https://github.com/Arijitag7/Criativo---A-Website-for-Project-Ideas.git" target="_blank"
                 class="button-gradient px-6 py-2 rounded-full text-white font-semibold hover:-translate-y-1 transition-all duration-300 inline-flex items-center gap-2">
                <i class="fab fa-github"></i>
                View Code
              </a>
            </div>
          </div>
          <div class="p-6">
            <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-2">Criativo - Project Ideas Website</h3>
            <p class="text-gray-600 dark:text-gray-300 text-sm">Full-stack web application for project collaboration</p>
          </div>
        </div>

        <!-- Project 3 -->
        <div class="group relative overflow-hidden rounded-3xl bg-white dark:bg-dark-card shadow-card hover:shadow-card-hover transition-all duration-500 floating-card">
          <div class="relative h-64 overflow-hidden">
            <img src="public/Sentimental.jpg" alt="Sentiment Analysis"
                 class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110" />
            <div class="project-overlay absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 flex flex-col justify-center items-center p-6 text-center">
              <h3 class="text-xl font-bold text-white mb-3">Customer Review Sentiment Analysis</h3>
              <p class="text-white/90 text-sm mb-4 text-justify">Used Azure Cognitive Services to extract sentiment insights from customer reviews to aid businesses in strategic decisions.</p>
              <a href="https://github.com/Arijitag7/Customer-Review-Sentimental-Analysis-using-Azure-Cognitive-Services.git" target="_blank"
                 class="button-gradient px-6 py-2 rounded-full text-white font-semibold hover:-translate-y-1 transition-all duration-300 inline-flex items-center gap-2">
                <i class="fab fa-github"></i>
                View Code
              </a>
            </div>
          </div>
          <div class="p-6">
            <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-2">Sentiment Analysis with Azure</h3>
            <p class="text-gray-600 dark:text-gray-300 text-sm">AI-powered sentiment analysis using cloud services</p>
          </div>
        </div>

        <!-- Project 4 -->
        <div class="group relative overflow-hidden rounded-3xl bg-white dark:bg-dark-card shadow-card hover:shadow-card-hover transition-all duration-500 floating-card">
          <div class="relative h-64 overflow-hidden">
            <img src="public/Electricity.jpg" alt="Billing System"
                 class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110" />
            <div class="project-overlay absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 flex flex-col justify-center items-center p-6 text-center">
              <h3 class="text-xl font-bold text-white mb-3">Electricity Billing System</h3>
              <p class="text-white/90 text-sm mb-4 text-justify">A Java application to automate electricity bill generation and manage user records efficiently through a simple interface.</p>
              <a href="https://github.com/Arijitag7/Electricity-Billing-System.git" target="_blank"
                 class="button-gradient px-6 py-2 rounded-full text-white font-semibold hover:-translate-y-1 transition-all duration-300 inline-flex items-center gap-2">
                <i class="fab fa-github"></i>
                View Code
              </a>
            </div>
          </div>
          <div class="p-6">
            <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-2">Electricity Billing System</h3>
            <p class="text-gray-600 dark:text-gray-300 text-sm">Desktop application built with Java Swing</p>
          </div>
        </div>

        <!-- Project 5 -->
        <div class="group relative overflow-hidden rounded-3xl bg-white dark:bg-dark-card shadow-card hover:shadow-card-hover transition-all duration-500 floating-card">
          <div class="relative h-64 overflow-hidden">
            <img src="public/Portfolio.jpg" alt="Portfolio"
                 class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110" />
            <div class="project-overlay absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 flex flex-col justify-center items-center p-6 text-center">
              <h3 class="text-xl font-bold text-white mb-3">Personal Portfolio Website</h3>
              <p class="text-white/90 text-sm mb-4 text-justify">A responsive portfolio website built using HTML and CSS. It showcases all my skills, projects, and contact information.</p>
              <a href="https://github.com/Arijitag7/new-portfolio.git" target="_blank"
                 class="button-gradient px-6 py-2 rounded-full text-white font-semibold hover:-translate-y-1 transition-all duration-300 inline-flex items-center gap-2">
                <i class="fab fa-github"></i>
                View Code
              </a>
            </div>
          </div>
          <div class="p-6">
            <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-2">Personal Portfolio Website</h3>
            <p class="text-gray-600 dark:text-gray-300 text-sm">Responsive web design with modern UI/UX</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Modern Contact Section -->
  <section id="contact" class="py-20 bg-gray-50 dark:bg-dark-surface">
    <div class="max-w-4xl mx-auto px-6">
      <!-- Section Header -->
      <div class="text-center mb-16">
        <h2 class="text-4xl md:text-5xl font-bold mb-4">
          <span class="text-gradient">Get In Touch</span>
        </h2>
        <div class="w-24 h-1 bg-gradient-to-r from-accent-primary to-accent-secondary mx-auto rounded-full"></div>
        <p class="text-gray-600 dark:text-gray-300 mt-4 text-lg">Let's discuss your next project</p>
      </div>

      <div class="glass-effect rounded-3xl p-8 md:p-12">
        <!-- Getform.io Contact Form -->
        <form id="contact-form" action="https://getform.io/f/bmdmydra" method="POST" class="contact-form space-y-6">
          <div class="grid md:grid-cols-2 gap-6">
            <div class="space-y-2">
              <label class="text-sm font-semibold text-gray-700 dark:text-gray-300">Name</label>
              <input type="text" name="name" placeholder="Your Name" required
                     class="w-full px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-gray-600
                            bg-white dark:bg-dark-card text-gray-900 dark:text-white
                            focus:border-accent-primary dark:focus:border-accent-primary
                            focus:outline-none transition-all duration-300" />
            </div>

            <div class="space-y-2">
              <label class="text-sm font-semibold text-gray-700 dark:text-gray-300">Email</label>
              <input type="email" name="email" placeholder="Your Email" required
                     class="w-full px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-gray-600
                            bg-white dark:bg-dark-card text-gray-900 dark:text-white
                            focus:border-accent-primary dark:focus:border-accent-primary
                            focus:outline-none transition-all duration-300" />
            </div>
          </div>

          <div class="space-y-2">
            <label class="text-sm font-semibold text-gray-700 dark:text-gray-300">Phone</label>
            <input type="tel" name="phone" placeholder="Your Phone Number"
                   class="w-full px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-gray-600
                          bg-white dark:bg-dark-card text-gray-900 dark:text-white
                          focus:border-accent-primary dark:focus:border-accent-primary
                          focus:outline-none transition-all duration-300" />
          </div>

          <div class="space-y-2">
            <label class="text-sm font-semibold text-gray-700 dark:text-gray-300">Subject</label>
            <select name="subject" required
                    class="w-full px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-gray-600
                           bg-white dark:bg-dark-card text-gray-900 dark:text-white
                           focus:border-accent-primary dark:focus:border-accent-primary
                           focus:outline-none transition-all duration-300">
              <option value="">Select a subject</option>
              <option value="General Inquiry">General Inquiry</option>
              <option value="Job Opportunity">Job Opportunity</option>
              <option value="Project Collaboration">Project Collaboration</option>
              <option value="Technical Question">Technical Question</option>
              <option value="Other">Other</option>
            </select>
          </div>

          <div class="space-y-2">
            <label class="text-sm font-semibold text-gray-700 dark:text-gray-300">Message</label>
            <textarea name="message" rows="6" placeholder="Tell me about your project..." required
                      class="w-full px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-gray-600
                             bg-white dark:bg-dark-card text-gray-900 dark:text-white
                             focus:border-accent-primary dark:focus:border-accent-primary
                             focus:outline-none transition-all duration-300 resize-none"></textarea>
          </div>

          <!-- Hidden fields for better tracking -->
          <input type="hidden" name="_gotcha" style="display:none !important">
          <input type="hidden" name="_subject" value="New Portfolio Contact from Website">
          <input type="hidden" name="_next" value="https://arijitdey.dev/thank-you">

          <button type="submit" id="submit-btn"
                  class="w-full button-gradient px-8 py-4 rounded-xl text-white font-semibold
                         shadow-glow hover:shadow-glow-lg transition-all duration-300
                         hover:-translate-y-1 inline-flex items-center justify-center gap-2">
            <i class="fas fa-paper-plane"></i>
            <span id="btn-text">Send Message</span>
          </button>
        </form>

        <!-- Success Message -->
        <div id="success-message" class="hidden text-center py-8">
          <div class="glass-effect rounded-xl p-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800">
            <i class="fas fa-check-circle text-4xl text-green-500 mb-4"></i>
            <h3 class="text-xl font-semibold text-green-800 dark:text-green-200 mb-2">Message Sent Successfully!</h3>
            <p class="text-green-600 dark:text-green-300">Thank you for reaching out. I'll get back to you soon!</p>
            <button onclick="resetForm()" class="mt-4 px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
              Send Another Message
            </button>
          </div>
        </div>

        <!-- Error Message -->
        <div id="error-message" class="hidden text-center py-8">
          <div class="glass-effect rounded-xl p-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800">
            <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
            <h3 class="text-xl font-semibold text-red-800 dark:text-red-200 mb-2">Error Sending Message</h3>
            <p class="text-red-600 dark:text-red-300">Something went wrong. Please try again or contact me directly.</p>
            <button onclick="resetForm()" class="mt-4 px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
              Try Again
            </button>
          </div>
        </div>
      </div>

        <!-- Contact Info -->
        <div class="mt-12 grid md:grid-cols-3 gap-6 text-center">
          <div class="glass-effect rounded-xl p-6">
            <i class="fas fa-envelope text-3xl text-accent-primary mb-3"></i>
            <h3 class="font-semibold text-gray-800 dark:text-white mb-2">Email</h3>
            <p class="text-gray-600 dark:text-gray-300"><EMAIL></p>
          </div>

          <div class="glass-effect rounded-xl p-6">
            <i class="fas fa-map-marker-alt text-3xl text-accent-primary mb-3"></i>
            <h3 class="font-semibold text-gray-800 dark:text-white mb-2">Location</h3>
            <p class="text-gray-600 dark:text-gray-300">Kolkata, India</p>
          </div>

          <div class="glass-effect rounded-xl p-6">
            <i class="fas fa-clock text-3xl text-accent-primary mb-3"></i>
            <h3 class="font-semibold text-gray-800 dark:text-white mb-2">Response Time</h3>
            <p class="text-gray-600 dark:text-gray-300">Within 24 hours</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Modern Footer -->
  <footer class="bg-white dark:bg-dark-bg border-t border-gray-200 dark:border-gray-700 py-12">
    <div class="max-w-7xl mx-auto px-6">
      <div class="text-center">
        <div class="text-2xl font-bold text-gradient mb-4">Arijit Dey</div>
        <p class="text-gray-600 dark:text-gray-300 mb-6">Software Developer & Cloud Enthusiast</p>

        <!-- Social Links -->
        <div class="flex justify-center space-x-6 mb-8">
          <a href="https://github.com/Arijitag7" target="_blank"
             class="w-12 h-12 bg-gray-100 dark:bg-dark-card rounded-full flex items-center justify-center
                    text-gray-600 dark:text-gray-300 hover:text-accent-primary hover:bg-accent-primary/10
                    transition-all duration-300 hover:scale-110">
            <i class="fab fa-github text-xl"></i>
          </a>
          <a href="https://www.linkedin.com/in/arijit-dey-b580b9231/" target="_blank"
             class="w-12 h-12 bg-gray-100 dark:bg-dark-card rounded-full flex items-center justify-center
                    text-gray-600 dark:text-gray-300 hover:text-accent-primary hover:bg-accent-primary/10
                    transition-all duration-300 hover:scale-110">
            <i class="fab fa-linkedin text-xl"></i>
          </a>
          <a href="https://www.instagram.com/_.ari.jit._/" target="_blank"
             class="w-12 h-12 bg-gray-100 dark:bg-dark-card rounded-full flex items-center justify-center
                    text-gray-600 dark:text-gray-300 hover:text-accent-primary hover:bg-accent-primary/10
                    transition-all duration-300 hover:scale-110">
            <i class="fab fa-instagram text-xl"></i>
            </a>  
            <a href="https://www.facebook.com/arijit.dey.7374480" target="_blank"
             class="w-12 h-12 bg-gray-100 dark:bg-dark-card rounded-full flex items-center justify-center
                    text-gray-600 dark:text-gray-300 hover:text-accent-primary hover:bg-accent-primary/10
                    transition-all duration-300 hover:scale-110">
            <i class="fab fa-facebook text-xl"></i>
            </a>
            <a href="https://x.com/ArijitD35751542" target="_blank"
             class="w-12 h-12 bg-gray-100 dark:bg-dark-card rounded-full flex items-center justify-center
                    text-gray-600 dark:text-gray-300 hover:text-accent-primary hover:bg-accent-primary/10
                    transition-all duration-300 hover:scale-110">
            <i class="fab fa-x-twitter text-xl"></i>
            </a>
        </div>

        <div class="border-t border-gray-300 dark:border-gray-700 pt-8">
          <p class="text-gray-500 dark:text-gray-400">
            &copy; 2025 Arijit Dey. All rights reserved. Built with ❤️ and modern web technologies.
          </p>
        </div>
      </div>
    </div>
  </footer>

  <!-- JavaScript -->
  <script>
    // Enhanced hamburger menu functionality
    const hamburger = document.getElementById("hamburger");
    const navLinks = document.getElementById("nav-links");

    hamburger.addEventListener("click", () => {
      hamburger.classList.toggle("active");
      navLinks.classList.toggle("active");
    });

    // Close mobile menu when clicking on a link
    const mobileNavLinks = navLinks.querySelectorAll("a");
    mobileNavLinks.forEach(link => {
      link.addEventListener("click", () => {
        hamburger.classList.remove("active");
        navLinks.classList.remove("active");
      });
    });

    // Close mobile menu when clicking outside
    document.addEventListener("click", (e) => {
      if (!hamburger.contains(e.target) && !navLinks.contains(e.target)) {
        hamburger.classList.remove("active");
        navLinks.classList.remove("active");
      }
    });

    // Dark mode toggle functionality
    const toggleBtn = document.getElementById('toggle-theme');

    // Check for saved theme preference or default to light mode
    const currentTheme = localStorage.getItem('theme') || 'light';
    if (currentTheme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }

    toggleBtn.addEventListener('click', () => {
      document.documentElement.classList.toggle('dark');
      const isDark = document.documentElement.classList.contains('dark');
      localStorage.setItem('theme', isDark ? 'dark' : 'light');
    });

    // Getform.io Contact Form functionality
    const form = document.getElementById('contact-form');
    const submitBtn = document.getElementById('submit-btn');
    const btnText = document.getElementById('btn-text');
    const successMessage = document.getElementById('success-message');
    const errorMessage = document.getElementById('error-message');

    // Reset form function
    function resetForm() {
      form.style.display = 'block';
      successMessage.classList.add('hidden');
      errorMessage.classList.add('hidden');
      form.reset();

      // Reset button state
      submitBtn.disabled = false;
      submitBtn.classList.remove('opacity-50', 'cursor-not-allowed');
      btnText.textContent = 'Send Message';
      submitBtn.querySelector('i').className = 'fas fa-paper-plane';
    }

    // Make resetForm globally available
    window.resetForm = resetForm;

    form.addEventListener('submit', async (e) => {
      e.preventDefault();

      // Validate required fields
      const name = form.elements['name'].value.trim();
      const email = form.elements['email'].value.trim();
      const subject = form.elements['subject'].value;
      const message = form.elements['message'].value.trim();

      if (!name || !email || !subject || !message) {
        alert('Please fill in all required fields.');
        return;
      }

      // Email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        alert('Please enter a valid email address.');
        return;
      }

      // Update button state to loading
      submitBtn.disabled = true;
      submitBtn.classList.add('opacity-50', 'cursor-not-allowed');
      btnText.textContent = 'Sending...';
      submitBtn.querySelector('i').className = 'fas fa-spinner fa-spin';

      try {
        // Create FormData object
        const formData = new FormData(form);

        // Submit to Getform.io
        const response = await fetch('https://getform.io/f/bmdmydra', {
          method: 'POST',
          body: formData
        });

        if (response.ok) {
          // Show success message
          form.style.display = 'none';
          successMessage.classList.remove('hidden');

          // Track successful submission (optional)
          if (typeof gtag !== 'undefined') {
            gtag('event', 'form_submit', {
              event_category: 'Contact',
              event_label: 'Portfolio Contact Form'
            });
          }

        } else {
          throw new Error('Form submission failed');
        }

      } catch (error) {
        console.error('Form submission error:', error);

        // Show error message
        form.style.display = 'none';
        errorMessage.classList.remove('hidden');

        // Track error (optional)
        if (typeof gtag !== 'undefined') {
          gtag('event', 'form_error', {
            event_category: 'Contact',
            event_label: 'Portfolio Contact Form Error'
          });
        }
      }
    });

    // Add real-time validation feedback
    const inputs = form.querySelectorAll('input[required], textarea[required], select[required]');
    inputs.forEach(input => {
      input.addEventListener('blur', function() {
        if (this.value.trim() === '') {
          this.style.borderColor = '#ef4444';
        } else if (this.type === 'email') {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (emailRegex.test(this.value)) {
            this.style.borderColor = '#10b981';
          } else {
            this.style.borderColor = '#ef4444';
          }
        } else {
          this.style.borderColor = '#10b981';
        }
      });

      input.addEventListener('focus', function() {
        this.style.borderColor = '#6366f1';
      });
    });

    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });

    // Add scroll effect to navigation
    window.addEventListener('scroll', () => {
      const nav = document.querySelector('nav');
      if (window.scrollY > 100) {
        nav.classList.add('backdrop-blur-lg');
      } else {
        nav.classList.remove('backdrop-blur-lg');
      }
    });

    // Enhanced active section highlighting on scroll
    function updateActiveNavigation() {
      const sections = [
        { id: 'header', element: document.querySelector('#header, header') },
        { id: 'about', element: document.querySelector('#about') },
        { id: 'skills', element: document.querySelector('#skills') },
        { id: 'education', element: document.querySelector('#education') },
        { id: 'projects', element: document.querySelector('#projects') },
        { id: 'contact', element: document.querySelector('#contact') }
      ];

      const navLinks = document.querySelectorAll('.nav-link, .mobile-nav-link');
      const scrollPosition = window.scrollY + 100;

      let currentSection = 'header'; // Default to header

      // Special handling for header (top of page)
      if (window.scrollY < 300) {
        currentSection = 'header';
      } else {
        // Check each section (skip header since we handled it above)
        sections.slice(1).forEach(section => {
          if (section.element) {
            const sectionTop = section.element.offsetTop;
            const sectionHeight = section.element.offsetHeight;

            // If we're in this section's range
            if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
              currentSection = section.id;
            }
          }
        });
      }

      // Update navigation links
      navLinks.forEach(link => {
        link.classList.remove('active');
        const href = link.getAttribute('href');

        if (href === `#${currentSection}`) {
          link.classList.add('active');
        }
      });

      // Debug: Log current section (remove in production)
      console.log(`Active section: ${currentSection}, Scroll: ${Math.round(window.scrollY)}`);
    }

    // Throttled scroll event for better performance
    let scrollTimeout;
    window.addEventListener('scroll', () => {
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }

      scrollTimeout = setTimeout(() => {
        updateActiveNavigation();
      }, 10);
    });

    // Initialize active navigation on page load
    document.addEventListener('DOMContentLoaded', () => {
      updateActiveNavigation();

      // Mobile-specific optimizations
      if (window.innerWidth <= 768) {
        // Prevent zoom on input focus for iOS
        const inputs = document.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
          input.addEventListener('focus', function() {
            if (this.style.fontSize !== '16px') {
              this.style.fontSize = '16px';
            }
          });
        });

        // Improve touch scrolling
        document.body.style.webkitOverflowScrolling = 'touch';

        // Add touch feedback for buttons
        const buttons = document.querySelectorAll('button, .mobile-nav-link, a[href^="#"]');
        buttons.forEach(button => {
          button.addEventListener('touchstart', function() {
            this.style.opacity = '0.7';
          });

          button.addEventListener('touchend', function() {
            setTimeout(() => {
              this.style.opacity = '';
            }, 150);
          });
        });
      }

      // Handle orientation changes
      window.addEventListener('orientationchange', function() {
        setTimeout(() => {
          // Recalculate viewport height
          const vh = window.innerHeight * 0.01;
          document.documentElement.style.setProperty('--vh', `${vh}px`);

          // Update navigation if needed
          updateActiveNavigation();
        }, 100);
      });

      // Set initial viewport height for mobile
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty('--vh', `${vh}px`);
    });

    // Enhanced mobile menu handling
    function handleMobileMenuToggle() {
      const hamburger = document.getElementById("hamburger");
      const navLinks = document.getElementById("nav-links");

      if (hamburger && navLinks) {
        const isActive = hamburger.classList.contains("active");

        if (isActive) {
          // Closing menu
          hamburger.classList.remove("active");
          navLinks.classList.remove("active");
          document.body.style.overflow = '';
        } else {
          // Opening menu
          hamburger.classList.add("active");
          navLinks.classList.add("active");
          document.body.style.overflow = 'hidden'; // Prevent background scroll
        }
      }
    }

    // Improve mobile navigation
    if (window.innerWidth <= 768) {
      const hamburger = document.getElementById("hamburger");
      if (hamburger) {
        hamburger.removeEventListener("click", () => {});
        hamburger.addEventListener("click", handleMobileMenuToggle);
      }
    }
  </script>
</body>
</html>