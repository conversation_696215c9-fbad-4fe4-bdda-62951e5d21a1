{"name": "arijit-portfolio", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["portfolio", "express", "nodejs", "contact-form"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "description": "Personal portfolio website with contact form backend", "dependencies": {"body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "nodemailer": "^7.0.3"}, "devDependencies": {"nodemon": "^3.0.0"}}